# 🛠️ Guia do Sistema de Ferramentas

Este guia explica como usar e criar ferramentas personalizadas para o agente LangGraph.

## 🎯 Visão Geral

O sistema de ferramentas permite que o agente execute ações específicas como:
- 🌤️ Consultar clima
- 🧮 Fazer cálculos matemáticos
- 🔍 Pesquisar na web
- 📁 Processar arquivos
- 🌐 Acessar APIs externas

## 🚀 Uso Básico

### Criando um Agente com Ferramentas

```python
from src.agent import LangGraphAgent, AgentConfig
from src.tools.weather import create_weather_tool
from src.tools.calculator import create_calculator_tool

# Criar ferramentas
tools = [
    create_weather_tool(api_key="sua_chave_openweather"),
    create_calculator_tool(precision=4)
]

# Criar agente com ferramentas
config = AgentConfig()
agent = LangGraphAgent(config, tools=tools)

# Usar o agente
response = agent.run("Qual é o clima em São Paulo e quanto é 2+2?")
```

### Gerenciamento Dinâmico

```python
# Adicionar ferramenta
weather_tool = create_weather_tool()
agent.add_tool(weather_tool)

# Remover ferramenta
agent.remove_tool("weather")

# Listar ferramentas
print(agent.list_enabled_tools())
```

## 🛠️ Ferramentas Disponíveis

### 1. 🌤️ WeatherTool - Clima

Obtém informações meteorológicas para uma cidade.

```python
from src.tools.weather import create_weather_tool

# Com API real (OpenWeatherMap)
weather_tool = create_weather_tool(
    api_key="sua_chave_openweather",
    units="metric",  # metric, imperial, kelvin
    enabled=True
)

# Com dados mock (para testes)
weather_tool = create_weather_tool(api_key=None)
```

**Parâmetros:**
- `city` (obrigatório): Nome da cidade
- `country_code` (opcional): Código do país (BR, US, etc.)
- `units` (opcional): Unidade de temperatura

**Exemplo de uso:**
```python
agent.run("Qual é o clima em São Paulo, BR?")
```

### 2. 🧮 CalculatorTool - Calculadora

Executa cálculos matemáticos complexos.

```python
from src.tools.calculator import create_calculator_tool

calculator_tool = create_calculator_tool(
    precision=10,        # Casas decimais
    allow_functions=True # Permitir funções matemáticas
)
```

**Parâmetros:**
- `expression` (obrigatório): Expressão matemática
- `precision` (opcional): Casas decimais do resultado

**Funções suportadas:**
- Básicas: `+`, `-`, `*`, `/`, `**`, `%`
- Matemáticas: `sqrt()`, `sin()`, `cos()`, `tan()`, `log()`, `exp()`
- Constantes: `pi`, `e`, `tau`

**Exemplo de uso:**
```python
agent.run("Calcule sqrt(144) + sin(pi/2)")
```

### 3. 🔍 WebSearchTool - Busca Web

Pesquisa informações na internet usando DuckDuckGo.

```python
from src.tools.web_search import create_web_search_tool

search_tool = create_web_search_tool(
    max_results=5,
    search_engine="duckduckgo",
    safe_search=True
)
```

**Parâmetros:**
- `query` (obrigatório): Termo de busca
- `max_results` (opcional): Número máximo de resultados
- `safe_search` (opcional): Filtro de segurança

**Exemplo de uso:**
```python
agent.run("Pesquise informações sobre LangGraph")
```

## 🔧 Criando Ferramentas Personalizadas

### Estrutura Base

```python
from src.tools.base import BaseTool, ToolConfig, ToolResult
from typing import List, Dict, Any

class MinhaFerramenta(BaseTool):
    """Descrição da minha ferramenta"""
    
    def _validate_config(self) -> None:
        """Validar configuração específica"""
        # Implementar validações necessárias
        pass
    
    @property
    def required_parameters(self) -> List[str]:
        """Parâmetros obrigatórios"""
        return ["parametro_obrigatorio"]
    
    @property
    def optional_parameters(self) -> List[str]:
        """Parâmetros opcionais"""
        return ["parametro_opcional"]
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Schema dos parâmetros para o LLM"""
        return {
            "parametro_obrigatorio": {
                "type": "string",
                "description": "Descrição do parâmetro"
            }
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Executar a ferramenta"""
        try:
            # Implementar lógica da ferramenta
            resultado = self._processar(kwargs)
            
            return ToolResult(
                success=True,
                data=resultado,
                metadata={"timestamp": "now"}
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    def _processar(self, params):
        """Lógica específica da ferramenta"""
        # Implementar aqui
        pass

# Função factory
def create_minha_ferramenta(param1="default", enabled=True):
    config = ToolConfig(
        name="minha_ferramenta",
        description="Descrição da ferramenta",
        enabled=enabled,
        parameters={"param1": param1}
    )
    return MinhaFerramenta(config)
```

### Exemplo: Ferramenta de Data/Hora

```python
import datetime
from src.tools.base import BaseTool, ToolConfig, ToolResult

class DateTimeTool(BaseTool):
    """Ferramenta para obter data e hora"""
    
    def _validate_config(self) -> None:
        pass
    
    @property
    def required_parameters(self) -> List[str]:
        return []
    
    @property
    def optional_parameters(self) -> List[str]:
        return ["timezone", "format"]
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "timezone": {
                "type": "string",
                "description": "Timezone (ex: UTC, America/Sao_Paulo)"
            },
            "format": {
                "type": "string", 
                "description": "Formato da data (ex: %Y-%m-%d %H:%M:%S)"
            }
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        try:
            timezone = kwargs.get("timezone", "UTC")
            format_str = kwargs.get("format", "%Y-%m-%d %H:%M:%S")
            
            now = datetime.datetime.now()
            formatted_time = now.strftime(format_str)
            
            return ToolResult(
                success=True,
                data={
                    "current_time": formatted_time,
                    "timezone": timezone,
                    "timestamp": now.timestamp()
                }
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e)
            )

def create_datetime_tool(enabled=True):
    config = ToolConfig(
        name="datetime",
        description="Get current date and time",
        enabled=enabled
    )
    return DateTimeTool(config)
```

## 📋 Configuração Avançada

### Configuração via AgentConfig

```python
config = AgentConfig(
    enabled_tools=["weather", "calculator"],
    tool_configs={
        "weather": {
            "api_key": "sua_chave",
            "units": "metric"
        },
        "calculator": {
            "precision": 5,
            "allow_functions": True
        }
    }
)
```

### Registro de Ferramentas

```python
from src.tools.base import ToolRegistry

# Criar registry
registry = ToolRegistry()

# Registrar classe de ferramenta
registry.register_tool_class(MinhaFerramenta, "minha_ferramenta")

# Criar instância
config = ToolConfig(name="test", description="Test tool")
tool = registry.create_tool("minha_ferramenta", config)

# Adicionar ao registry
registry.add_tool(tool)
```

## 🔒 Segurança

### Validação de Entrada
- Sempre validar parâmetros de entrada
- Sanitizar dados antes de processar
- Implementar timeouts para operações externas

### Tratamento de Erros
```python
async def execute(self, **kwargs) -> ToolResult:
    try:
        # Validar entrada
        if not self._validate_input(kwargs):
            return ToolResult(
                success=False,
                error="Invalid input parameters"
            )
        
        # Executar com timeout
        result = await asyncio.wait_for(
            self._execute_operation(kwargs),
            timeout=30.0
        )
        
        return ToolResult(success=True, data=result)
        
    except asyncio.TimeoutError:
        return ToolResult(
            success=False,
            error="Operation timed out"
        )
    except Exception as e:
        logger.error(f"Tool execution error: {e}")
        return ToolResult(
            success=False,
            error="Internal tool error"
        )
```

## 🧪 Testes

### Testando Ferramentas

```python
import pytest
from src.tools.weather import create_weather_tool

@pytest.mark.asyncio
async def test_weather_tool():
    tool = create_weather_tool(api_key=None)  # Mock data
    
    result = await tool.execute(city="São Paulo")
    
    assert result.success
    assert "temperature" in result.data
    assert result.data["location"] == "São Paulo"

def test_tool_schema():
    tool = create_weather_tool()
    schema = tool.get_schema()
    
    assert schema["name"] == "weather"
    assert "city" in schema["parameters"]["required"]
```

## 📚 Exemplos Práticos

### Executar Exemplos

```bash
# Exemplo básico com ferramentas
python examples/tools_example.py

# Modo interativo
python examples/basic_usage.py --mode interactive
```

### Casos de Uso

1. **Assistente Meteorológico**
   ```python
   agent.run("Qual é o clima em 5 cidades brasileiras?")
   ```

2. **Calculadora Científica**
   ```python
   agent.run("Calcule a área de um círculo com raio 5")
   ```

3. **Pesquisador Web**
   ```python
   agent.run("Pesquise as últimas notícias sobre IA")
   ```

4. **Assistente Multifuncional**
   ```python
   agent.run("Pesquise sobre Python, calcule 15% de 200 e me diga o clima em Paris")
   ```

## 🔮 Próximos Passos

- [ ] Ferramenta de processamento de arquivos
- [ ] Integração com APIs de terceiros
- [ ] Ferramenta de geração de código
- [ ] Sistema de cache para ferramentas
- [ ] Ferramentas de banco de dados

---

🎉 **Agora você pode criar agentes poderosos com ferramentas personalizadas!**
