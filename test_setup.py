#!/usr/bin/env python3
"""
Script para testar se o setup do projeto está funcionando corretamente
"""

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test if all imports work correctly"""
    try:
        from agent import LangGraphAgent, AgentConfig
        print("✅ Imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_config():
    """Test configuration creation"""
    try:
        from agent import AgentConfig
        config = AgentConfig()
        print("✅ Configuration creation successful")
        print(f"   Agent Name: {config.agent_name}")
        print(f"   Model: {config.model_name}")
        print(f"   Temperature: {config.temperature}")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_agent_creation():
    """Test agent creation (without API key)"""
    try:
        from agent import LangGraphAgent, AgentConfig

        # Test with dummy API key
        config = AgentConfig(groq_api_key="test-key")

        # This will fail at LLM initialization, but we can catch that
        try:
            agent = LangGraphAgent(config)
            print("✅ Agent creation successful (with test key)")
            return True
        except Exception as e:
            if "api" in str(e).lower() or "key" in str(e).lower():
                print("✅ Agent creation test passed (API key validation working)")
                return True
            else:
                print(f"❌ Unexpected agent creation error: {e}")
                return False
    except Exception as e:
        print(f"❌ Agent creation error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing LangGraph Agent Setup")
    print("=" * 40)

    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_config),
        ("Agent Creation Test", test_agent_creation),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        if test_func():
            passed += 1
        print("-" * 30)

    print(f"\n📊 Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Your setup is ready.")
        print("\n📝 Next steps:")
        print("1. Copy .env.example to .env")
        print("2. Add your Groq API key to .env")
        print("3. Run: python examples/basic_usage.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
