"""
Tests for the configuration module
"""

import pytest
import os
from unittest.mock import patch
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from agent.config import AgentConfig


class TestAgentConfig:
    """Test cases for AgentConfig"""

    def test_default_config(self):
        """Test default configuration values"""
        config = AgentConfig()

        assert config.agent_name == "LangGraphAgent"
        assert config.model_name == "meta-llama/llama-4-maverick-17b-128e-instruct"
        assert config.temperature == 0.1
        assert config.max_tokens == 1000
        assert config.log_level == "INFO"

    @patch.dict(os.environ, {
        'GROQ_API_KEY': 'test-key',
        'AGENT_NAME': 'TestAgent',
        'LOG_LEVEL': 'DEBUG'
    })
    def test_env_config(self):
        """Test configuration from environment variables"""
        config = AgentConfig()

        assert config.groq_api_key == "test-key"
        assert config.agent_name == "TestAgent"
        assert config.log_level == "DEBUG"

    def test_validate_config_success(self):
        """Test successful configuration validation"""
        config = AgentConfig(groq_api_key="test-key")
        assert config.validate_config() is True

    def test_validate_config_failure(self):
        """Test configuration validation failure"""
        config = AgentConfig(groq_api_key="")

        with pytest.raises(ValueError, match="Groq API key is required"):
            config.validate_config()

    def test_custom_config(self):
        """Test custom configuration values"""
        config = AgentConfig(
            groq_api_key="custom-key",
            agent_name="CustomAgent",
            model_name="meta-llama/llama-3.1-70b-versatile",
            temperature=0.5,
            max_tokens=2000
        )

        assert config.groq_api_key == "custom-key"
        assert config.agent_name == "CustomAgent"
        assert config.model_name == "meta-llama/llama-3.1-70b-versatile"
        assert config.temperature == 0.5
        assert config.max_tokens == 2000
