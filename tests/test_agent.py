"""
Tests for the LangGraph Agent
"""

import pytest
from unittest.mock import Mock, patch
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from agent.agent import LangGraphAgent, AgentState
from agent.config import AgentConfig
from langchain_core.messages import HumanMessage, AIMessage


class TestLangGraphAgent:
    """Test cases for LangGraphAgent"""

    @patch('agent.agent.ChatGroq')
    def test_agent_initialization(self, mock_chat_groq):
        """Test agent initialization"""
        config = AgentConfig(groq_api_key="test-key")
        agent = LangGraphAgent(config)

        assert agent.config == config
        assert agent.llm is not None
        assert agent.graph is not None
        mock_chat_groq.assert_called_once()

    def test_analyze_task(self):
        """Test task analysis"""
        config = AgentConfig(groq_api_key="test-key")

        with patch('agent.agent.ChatGroq'):
            agent = LangGraphAgent(config)

        # Test with human message
        state = AgentState(
            messages=[HumanMessage(content="Test task")],
            current_task=None,
            context={}
        )

        result = agent._analyze_task(state)
        assert result["current_task"] == "Test task"

    def test_process_task(self):
        """Test task processing"""
        config = AgentConfig(groq_api_key="test-key")

        with patch('agent.agent.ChatGroq'):
            agent = LangGraphAgent(config)

        state = AgentState(
            messages=[],
            current_task="Test task",
            context={}
        )

        result = agent._process_task(state)
        assert "task_type" in result["context"]
        assert "complexity" in result["context"]
        assert "processed_at" in result["context"]

    @patch('agent.agent.ChatGroq')
    def test_generate_response(self, mock_chat_groq):
        """Test response generation"""
        # Mock the LLM response
        mock_llm = Mock()
        mock_response = AIMessage(content="Test response")
        mock_llm.invoke.return_value = mock_response
        mock_chat_groq.return_value = mock_llm

        config = AgentConfig(groq_api_key="test-key")
        agent = LangGraphAgent(config)

        state = AgentState(
            messages=[HumanMessage(content="Test input")],
            current_task="Test task",
            context={"test": "context"}
        )

        result = agent._generate_response(state)

        # Check that the response was added to messages
        assert len(result["messages"]) == 2
        assert isinstance(result["messages"][-1], AIMessage)
        assert result["messages"][-1].content == "Test response"

    @patch('agent.agent.ChatGroq')
    def test_run_method(self, mock_chat_groq):
        """Test the run method"""
        # Mock the LLM response
        mock_llm = Mock()
        mock_response = AIMessage(content="Hello! I'm a test response.")
        mock_llm.invoke.return_value = mock_response
        mock_chat_groq.return_value = mock_llm

        config = AgentConfig(groq_api_key="test-key")
        agent = LangGraphAgent(config)

        response = agent.run("Hello, agent!")

        assert response == "Hello! I'm a test response."
        mock_llm.invoke.assert_called_once()

    @pytest.mark.asyncio
    @patch('agent.agent.ChatGroq')
    async def test_arun_method(self, mock_chat_groq):
        """Test the async run method"""
        # Mock the LLM response
        mock_llm = Mock()
        mock_response = AIMessage(content="Async test response.")
        mock_llm.invoke.return_value = mock_response
        mock_chat_groq.return_value = mock_llm

        config = AgentConfig(groq_api_key="test-key")
        agent = LangGraphAgent(config)

        # Mock the async invoke method
        async def mock_ainvoke(state):
            return agent.graph.invoke(state)

        agent.graph.ainvoke = mock_ainvoke

        response = await agent.arun("Hello, async agent!")

        assert response == "Async test response."
