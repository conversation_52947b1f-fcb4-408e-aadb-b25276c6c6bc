# 🚀 Configuração do Groq para LangGraph Agent

E<PERSON> guia explica como configurar o Groq para usar com seu agente LangGraph.

## 🤔 Por que Groq?

O **Groq** é uma plataforma de inferência de IA que oferece:

- ⚡ **Velocidade Ultra-Rápida**: Inferência até 10x mais rápida que outros provedores
- 🆓 **Tier Gratuito Generoso**: Milhares de tokens gratuitos por dia
- 🦙 **Modelos Llama de Última Geração**: Acesso aos melhores modelos da Meta
- 🔧 **API Simples**: Compatível com OpenAI API
- 💰 **Custo-Benefício**: Preços competitivos para uso comercial

## 📝 Como Obter sua Chave da API

### Passo 1: Criar Conta
1. Acesse [console.groq.com](https://console.groq.com/)
2. Clique em "Sign Up" ou "Get Started"
3. Crie sua conta usando email ou GitHub

### Passo 2: Gerar Chave da API
1. Após fazer login, vá para a seção "API Keys"
2. Clique em "Create API Key"
3. Dê um nome para sua chave (ex: "LangGraph Agent")
4. Copie a chave gerada (ela só será mostrada uma vez!)

### Passo 3: Configurar no Projeto
1. Copie o arquivo de exemplo:
   ```bash
   cp .env.example .env
   ```

2. Edite o arquivo `.env` e adicione sua chave:
   ```env
   GROQ_API_KEY=gsk_sua_chave_aqui
   ```

## 🦙 Modelos Disponíveis

O projeto está configurado para usar o **Meta Llama 4 Maverick**, mas você pode escolher entre:

### Modelos Llama 4 (Mais Recentes)
- `meta-llama/llama-4-maverick-17b-128e-instruct` ⭐ (Padrão)
- `meta-llama/llama-4-maverick-8b-128e-instruct`

### Modelos Llama 3.1
- `meta-llama/llama-3.1-70b-versatile` (Mais capaz)
- `meta-llama/llama-3.1-8b-instant` (Mais rápido)

### Outros Modelos
- `mixtral-8x7b-32768` (Mixtral da Mistral AI)
- `gemma2-9b-it` (Gemma do Google)

## ⚙️ Configuração Personalizada

Para usar um modelo diferente, modifique o arquivo `.env`:

```env
GROQ_API_KEY=sua_chave_aqui
MODEL_NAME=meta-llama/llama-3.1-70b-versatile
```

Ou configure diretamente no código:

```python
from src.agent import AgentConfig, LangGraphAgent

config = AgentConfig(
    groq_api_key="sua_chave",
    model_name="meta-llama/llama-3.1-70b-versatile",
    temperature=0.7,
    max_tokens=2000
)

agent = LangGraphAgent(config)
```

## 🧪 Testando a Configuração

### Teste Básico
```bash
python test_setup.py
```

### Teste com Exemplos
```bash
python examples/basic_usage.py --mode interactive
```

### Demonstração Completa do Groq
```bash
python examples/groq_llama_example.py
```

## 📊 Limites e Quotas

### Tier Gratuito
- **Requests por minuto**: 30
- **Requests por dia**: 14,400
- **Tokens por minuto**: 6,000
- **Tokens por dia**: 1,000,000

### Tier Pago
- Limites muito maiores
- Preços competitivos
- Suporte prioritário

## 🔧 Solução de Problemas

### Erro: "Invalid API Key"
- ✅ Verifique se a chave está correta no arquivo `.env`
- ✅ Certifique-se de que não há espaços extras
- ✅ A chave deve começar com `gsk_`

### Erro: "Rate Limit Exceeded"
- ⏰ Aguarde alguns minutos
- 📈 Considere upgrade para tier pago
- 🔄 Implemente retry logic no seu código

### Erro: "Model Not Found"
- 📝 Verifique se o nome do modelo está correto
- 🔍 Consulte a lista de modelos disponíveis no console Groq

### Resposta Lenta
- 🌐 Verifique sua conexão com a internet
- 🔄 Tente um modelo mais rápido (8B ao invés de 70B)
- ⚡ O Groq é normalmente muito rápido - pode ser um problema temporário

## 🚀 Próximos Passos

1. **Configure sua chave**: Siga os passos acima
2. **Teste o agente**: Execute os exemplos
3. **Explore modelos**: Teste diferentes modelos Llama
4. **Desenvolva**: Comece a construir seu agente personalizado

## 📚 Recursos Úteis

- [Groq Console](https://console.groq.com/)
- [Groq Documentation](https://console.groq.com/docs)
- [Groq Playground](https://console.groq.com/playground)
- [Meta Llama Models](https://llama.meta.com/)
- [LangChain Groq Integration](https://python.langchain.com/docs/integrations/chat/groq)

## 💡 Dicas Pro

1. **Use modelos menores para desenvolvimento**: 8B é mais rápido para testes
2. **Monitore seu uso**: Acompanhe no console Groq
3. **Implemente cache**: Para evitar chamadas desnecessárias
4. **Teste diferentes temperaturas**: 0.1 para precisão, 0.7 para criatividade
5. **Configure timeouts**: Para evitar travamentos

---

🎉 **Pronto!** Agora você pode usar o poder do Groq com Meta Llama em seu agente LangGraph!
