#!/usr/bin/env python3

import sys
import os
sys.path.append('src')

try:
    print("Testing base import...")
    from tools.base import BaseTool
    print("✅ Base imported")
except Exception as e:
    print(f"❌ Base import failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("Testing weather import...")
    from tools.weather import WeatherTool
    print("✅ Weather imported")
except Exception as e:
    print(f"❌ Weather import failed: {e}")
    import traceback
    traceback.print_exc()
