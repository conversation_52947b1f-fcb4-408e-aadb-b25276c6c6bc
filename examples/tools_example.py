"""
Exemplo demonstrando como usar ferramentas com o agente LangGraph
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from agent import LangGraphAgent, AgentConfig
from tools import WeatherTool, CalculatorTool, WebSearchTool
from tools.weather import create_weather_tool
from tools.calculator import create_calculator_tool
from tools.web_search import create_web_search_tool


def example_weather_tool():
    """Exemplo usando ferramenta de clima"""
    print("🌤️  Exemplo: Agente com Ferramenta de Clima")
    print("=" * 50)
    
    # Criar configuração
    config = AgentConfig()
    
    if not config.groq_api_key:
        print("❌ Por favor, configure sua GROQ_API_KEY no arquivo .env")
        return
    
    # Criar ferramenta de clima (sem API key = dados mock)
    weather_tool = create_weather_tool(
        api_key=None,  # Usar dados mock para demonstração
        units="metric",
        enabled=True
    )
    
    # Criar agente com a ferramenta
    agent = LangGraphAgent(config, tools=[weather_tool])
    
    print(f"🤖 Agente criado com ferramentas: {agent.list_enabled_tools()}")
    
    # Testar perguntas sobre clima
    test_questions = [
        "Qual é o clima em São Paulo?",
        "Como está o tempo em Londres?",
        "Me diga a temperatura em Nova York",
        "Está chovendo no Rio de Janeiro?"
    ]
    
    for question in test_questions:
        print(f"\n❓ Pergunta: {question}")
        try:
            response = agent.run(question)
            print(f"🤖 Resposta: {response}")
        except Exception as e:
            print(f"❌ Erro: {e}")
        print("-" * 30)


def example_calculator_tool():
    """Exemplo usando ferramenta de calculadora"""
    print("\n🧮 Exemplo: Agente com Ferramenta de Calculadora")
    print("=" * 50)
    
    # Criar configuração
    config = AgentConfig()
    
    if not config.groq_api_key:
        print("❌ Por favor, configure sua GROQ_API_KEY no arquivo .env")
        return
    
    # Criar ferramenta de calculadora
    calculator_tool = create_calculator_tool(
        precision=4,
        allow_functions=True,
        enabled=True
    )
    
    # Criar agente com a ferramenta
    agent = LangGraphAgent(config, tools=[calculator_tool])
    
    print(f"🤖 Agente criado com ferramentas: {agent.list_enabled_tools()}")
    
    # Testar cálculos
    test_calculations = [
        "Quanto é 15 * 23 + 47?",
        "Calcule a raiz quadrada de 144",
        "Qual é o seno de pi/2?",
        "Me ajude a calcular 2^10"
    ]
    
    for calculation in test_calculations:
        print(f"\n❓ Pergunta: {calculation}")
        try:
            response = agent.run(calculation)
            print(f"🤖 Resposta: {response}")
        except Exception as e:
            print(f"❌ Erro: {e}")
        print("-" * 30)


def example_multiple_tools():
    """Exemplo usando múltiplas ferramentas"""
    print("\n🛠️  Exemplo: Agente com Múltiplas Ferramentas")
    print("=" * 50)
    
    # Criar configuração
    config = AgentConfig()
    
    if not config.groq_api_key:
        print("❌ Por favor, configure sua GROQ_API_KEY no arquivo .env")
        return
    
    # Criar múltiplas ferramentas
    tools = [
        create_weather_tool(api_key=None, enabled=True),
        create_calculator_tool(precision=2, enabled=True),
        create_web_search_tool(max_results=3, enabled=True)
    ]
    
    # Criar agente com múltiplas ferramentas
    agent = LangGraphAgent(config, tools=tools)
    
    print(f"🤖 Agente criado com ferramentas: {agent.list_enabled_tools()}")
    print(f"📋 Informações das ferramentas:")
    for name, info in agent.get_tool_info().items():
        print(f"  - {name}: {info['description']}")
    
    # Testar perguntas que podem usar diferentes ferramentas
    test_questions = [
        "Qual é o clima em Paris e quanto é 25 + 30?",
        "Pesquise informações sobre LangGraph",
        "Calcule 15% de 200 e me diga o clima em Tóquio",
        "Busque informações sobre inteligência artificial"
    ]
    
    for question in test_questions:
        print(f"\n❓ Pergunta: {question}")
        try:
            response = agent.run(question)
            print(f"🤖 Resposta: {response}")
        except Exception as e:
            print(f"❌ Erro: {e}")
        print("-" * 30)


def example_dynamic_tools():
    """Exemplo adicionando e removendo ferramentas dinamicamente"""
    print("\n🔄 Exemplo: Gerenciamento Dinâmico de Ferramentas")
    print("=" * 50)
    
    # Criar configuração
    config = AgentConfig()
    
    if not config.groq_api_key:
        print("❌ Por favor, configure sua GROQ_API_KEY no arquivo .env")
        return
    
    # Criar agente sem ferramentas
    agent = LangGraphAgent(config)
    
    print(f"🤖 Agente criado sem ferramentas: {agent.list_enabled_tools()}")
    
    # Testar sem ferramentas
    print(f"\n❓ Pergunta (sem ferramentas): Qual é o clima em São Paulo?")
    response = agent.run("Qual é o clima em São Paulo?")
    print(f"🤖 Resposta: {response}")
    
    # Adicionar ferramenta de clima
    print(f"\n➕ Adicionando ferramenta de clima...")
    weather_tool = create_weather_tool(api_key=None, enabled=True)
    agent.add_tool(weather_tool)
    
    print(f"🤖 Ferramentas disponíveis: {agent.list_enabled_tools()}")
    
    # Testar com ferramenta de clima
    print(f"\n❓ Pergunta (com ferramenta): Qual é o clima em São Paulo?")
    response = agent.run("Qual é o clima em São Paulo?")
    print(f"🤖 Resposta: {response}")
    
    # Adicionar ferramenta de calculadora
    print(f"\n➕ Adicionando ferramenta de calculadora...")
    calculator_tool = create_calculator_tool(enabled=True)
    agent.add_tool(calculator_tool)
    
    print(f"🤖 Ferramentas disponíveis: {agent.list_enabled_tools()}")
    
    # Testar com ambas as ferramentas
    print(f"\n❓ Pergunta (múltiplas ferramentas): Qual é 2+2 e o clima em Londres?")
    response = agent.run("Qual é 2+2 e o clima em Londres?")
    print(f"🤖 Resposta: {response}")
    
    # Remover ferramenta de clima
    print(f"\n➖ Removendo ferramenta de clima...")
    agent.remove_tool("weather")
    
    print(f"🤖 Ferramentas disponíveis: {agent.list_enabled_tools()}")
    
    # Testar apenas com calculadora
    print(f"\n❓ Pergunta (só calculadora): Quanto é 10 * 5?")
    response = agent.run("Quanto é 10 * 5?")
    print(f"🤖 Resposta: {response}")


def interactive_tools_demo():
    """Demonstração interativa com ferramentas"""
    print("\n💬 Demonstração Interativa com Ferramentas")
    print("=" * 50)
    
    # Criar configuração
    config = AgentConfig()
    
    if not config.groq_api_key:
        print("❌ Por favor, configure sua GROQ_API_KEY no arquivo .env")
        return
    
    # Criar todas as ferramentas
    tools = [
        create_weather_tool(api_key=None, enabled=True),
        create_calculator_tool(precision=3, enabled=True),
        create_web_search_tool(max_results=3, enabled=True)
    ]
    
    # Criar agente
    agent = LangGraphAgent(config, tools=tools)
    
    print(f"🤖 {config.agent_name} com ferramentas ativas!")
    print(f"🛠️  Ferramentas disponíveis: {', '.join(agent.list_enabled_tools())}")
    print("💡 Exemplos de perguntas:")
    print("   - 'Qual é o clima em Paris?'")
    print("   - 'Calcule 15 * 23'")
    print("   - 'Pesquise sobre Python'")
    print("   - 'Quanto é 2+2 e o clima em Londres?'")
    print("💡 Digite 'quit' para sair")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("\n👤 Você: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'sair']:
                print("👋 Até logo!")
                break
            
            if not user_input:
                continue
            
            print("🤖 Processando...")
            response = agent.run(user_input)
            print(f"🤖 {config.agent_name}: {response}")
            
        except KeyboardInterrupt:
            print("\n👋 Até logo!")
            break
        except Exception as e:
            print(f"❌ Erro: {e}")


def main():
    """Função principal"""
    print("🛠️  Demonstração do Sistema de Ferramentas LangGraph")
    print("=" * 60)
    
    # Executar exemplos
    example_weather_tool()
    example_calculator_tool()
    example_multiple_tools()
    example_dynamic_tools()
    
    # Perguntar se quer modo interativo
    print("\n" + "=" * 60)
    choice = input("🤔 Deseja testar o modo interativo? (y/n): ").lower()
    if choice == 'y':
        interactive_tools_demo()
    
    print("\n✅ Demonstração concluída!")
    print("📚 Para mais informações, consulte a documentação das ferramentas.")


if __name__ == "__main__":
    main()
