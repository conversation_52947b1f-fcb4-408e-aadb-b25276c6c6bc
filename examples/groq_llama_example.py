"""
Exemplo específico para demonstrar o uso do agente com Groq e Llama
"""

import asyncio
import sys
import os
import time

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from agent import LangGraphAgent, AgentConfig


def demonstrate_groq_speed():
    """Demonstra a velocidade de inferência do Groq com Llama"""
    
    # Create configuration
    config = AgentConfig()
    
    # Check if Groq API key is set
    if not config.groq_api_key:
        print("❌ Por favor, configure sua GROQ_API_KEY no arquivo .env")
        print("📝 Você pode obter uma chave gratuita em: https://console.groq.com/")
        return
    
    # Create the agent
    agent = LangGraphAgent(config)
    
    print("🚀 Demonstração: Groq + Meta Llama")
    print("=" * 50)
    print(f"🤖 Modelo: {config.model_name}")
    print(f"🔥 Provider: Groq (Inferência Ultra-Rápida)")
    print("-" * 50)
    
    # Test prompts to demonstrate capabilities
    test_prompts = [
        {
            "prompt": "Explique em 2 parágrafos o que é LangGraph e suas vantagens.",
            "description": "Explicação técnica"
        },
        {
            "prompt": "Crie um pequeno poema sobre inteligência artificial.",
            "description": "Criatividade"
        },
        {
            "prompt": "Liste 5 vantagens do Groq sobre outros provedores de LLM.",
            "description": "Conhecimento específico"
        },
        {
            "prompt": "Como você processaria uma tarefa complexa usando grafos de estado?",
            "description": "Raciocínio técnico"
        }
    ]
    
    total_time = 0
    
    for i, test in enumerate(test_prompts, 1):
        print(f"\n🔹 Teste {i}: {test['description']}")
        print(f"❓ Pergunta: {test['prompt']}")
        print("⏱️  Processando...")
        
        start_time = time.time()
        try:
            response = agent.run(test['prompt'])
            end_time = time.time()
            
            response_time = end_time - start_time
            total_time += response_time
            
            print(f"⚡ Tempo de resposta: {response_time:.2f}s")
            print(f"🤖 Resposta: {response}")
            
        except Exception as e:
            print(f"❌ Erro: {e}")
        
        print("-" * 30)
    
    print(f"\n📊 Estatísticas:")
    print(f"⏱️  Tempo total: {total_time:.2f}s")
    print(f"📈 Tempo médio por resposta: {total_time/len(test_prompts):.2f}s")
    print(f"🚀 Velocidade do Groq: Ultra-rápida!")


async def demonstrate_async_processing():
    """Demonstra processamento assíncrono com Groq"""
    
    config = AgentConfig()
    
    if not config.groq_api_key:
        print("❌ Por favor, configure sua GROQ_API_KEY no arquivo .env")
        return
    
    agent = LangGraphAgent(config)
    
    print("\n🔄 Demonstração: Processamento Assíncrono")
    print("=" * 50)
    
    # Multiple prompts to process concurrently
    prompts = [
        "Qual é a capital do Brasil?",
        "Explique o conceito de machine learning.",
        "Como funciona o protocolo HTTP?",
        "O que são microserviços?"
    ]
    
    print("🚀 Processando múltiplas perguntas simultaneamente...")
    start_time = time.time()
    
    # Process all prompts concurrently
    tasks = [agent.arun(prompt) for prompt in prompts]
    responses = await asyncio.gather(*tasks)
    
    end_time = time.time()
    
    print(f"⚡ Tempo total (processamento paralelo): {end_time - start_time:.2f}s")
    print("\n📋 Resultados:")
    
    for i, (prompt, response) in enumerate(zip(prompts, responses), 1):
        print(f"\n{i}. ❓ {prompt}")
        print(f"   🤖 {response[:100]}...")


def compare_models():
    """Compara diferentes modelos disponíveis no Groq"""
    
    print("\n🔬 Comparação de Modelos Groq")
    print("=" * 50)
    
    # Different Groq models to test
    models = [
        "meta-llama/llama-4-maverick-17b-128e-instruct",
        "meta-llama/llama-3.1-70b-versatile",
        "meta-llama/llama-3.1-8b-instant",
        "mixtral-8x7b-32768"
    ]
    
    test_prompt = "Explique brevemente o que é um agente de IA."
    
    for model in models:
        try:
            config = AgentConfig(
                groq_api_key=os.getenv("GROQ_API_KEY", ""),
                model_name=model
            )
            
            if not config.groq_api_key:
                print("❌ GROQ_API_KEY não configurada")
                break
            
            agent = LangGraphAgent(config)
            
            print(f"\n🤖 Testando: {model}")
            start_time = time.time()
            
            response = agent.run(test_prompt)
            
            end_time = time.time()
            
            print(f"⏱️  Tempo: {end_time - start_time:.2f}s")
            print(f"📝 Resposta: {response[:150]}...")
            
        except Exception as e:
            print(f"❌ Erro com {model}: {e}")
        
        print("-" * 30)


def main():
    """Função principal"""
    
    print("🦙 Groq + Meta Llama - Demonstração Completa")
    print("=" * 60)
    
    # Test basic functionality
    demonstrate_groq_speed()
    
    # Test async processing
    asyncio.run(demonstrate_async_processing())
    
    # Compare models (optional)
    print("\n" + "=" * 60)
    choice = input("🤔 Deseja testar diferentes modelos? (y/n): ").lower()
    if choice == 'y':
        compare_models()
    
    print("\n✅ Demonstração concluída!")
    print("📚 Para mais informações:")
    print("   - Groq Console: https://console.groq.com/")
    print("   - LangGraph Docs: https://langchain-ai.github.io/langgraph/")


if __name__ == "__main__":
    main()
