#!/usr/bin/env python3
"""
Demonstração simples do sistema de ferramentas
"""

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def demo_weather_tool():
    """Demonstração da ferramenta de clima"""
    print("🌤️  Demonstração: Ferramenta de Clima")
    print("=" * 40)
    
    from tools.weather import create_weather_tool
    
    # Criar ferramenta (sem API key = dados mock)
    weather_tool = create_weather_tool(api_key=None)
    
    print(f"📋 Ferramenta: {weather_tool.name}")
    print(f"📝 Descrição: {weather_tool.description}")
    print(f"🔧 Parâmetros obrigatórios: {weather_tool.required_parameters}")
    print(f"⚙️  Parâmetros opcionais: {weather_tool.optional_parameters}")
    
    # Testar execução
    print(f"\n🧪 Testando execução...")
    result = weather_tool.execute_sync(city="São Paulo")
    
    if result.success:
        print(f"✅ Sucesso!")
        print(f"📊 Dados: {result.data}")
    else:
        print(f"❌ Erro: {result.error}")

def demo_calculator_tool():
    """Demonstração da ferramenta de calculadora"""
    print("\n🧮 Demonstração: Ferramenta de Calculadora")
    print("=" * 40)
    
    from tools.calculator import create_calculator_tool
    
    # Criar ferramenta
    calc_tool = create_calculator_tool(precision=3)
    
    print(f"📋 Ferramenta: {calc_tool.name}")
    print(f"📝 Descrição: {calc_tool.description}")
    
    # Testar cálculos
    expressions = [
        "2 + 3 * 4",
        "sqrt(16)",
        "sin(pi/2)",
        "15 * 23"
    ]
    
    for expr in expressions:
        print(f"\n🧪 Calculando: {expr}")
        result = calc_tool.execute_sync(expression=expr)
        
        if result.success:
            print(f"✅ Resultado: {result.data['result']}")
        else:
            print(f"❌ Erro: {result.error}")

def demo_tool_registry():
    """Demonstração do registry de ferramentas"""
    print("\n📋 Demonstração: Registry de Ferramentas")
    print("=" * 40)
    
    from tools.base import ToolRegistry
    from tools.weather import create_weather_tool
    from tools.calculator import create_calculator_tool
    
    # Criar registry
    registry = ToolRegistry()
    
    # Criar e adicionar ferramentas
    weather_tool = create_weather_tool(api_key=None)
    calc_tool = create_calculator_tool()
    
    registry.add_tool(weather_tool)
    registry.add_tool(calc_tool)
    
    print(f"🛠️  Ferramentas no registry: {registry.list_tools()}")
    print(f"✅ Ferramentas ativas: {registry.list_enabled_tools()}")
    print(f"📊 Total de ferramentas: {len(registry)}")
    
    # Testar execução via registry
    print(f"\n🧪 Testando execução via registry...")
    
    # Teste clima
    result = registry.execute_tool("weather", city="Rio de Janeiro")
    # Como é async, vamos usar asyncio
    import asyncio
    result = asyncio.run(result)
    
    if result.success:
        print(f"✅ Clima: {result.data['location']} - {result.data['temperature']}°C")
    else:
        print(f"❌ Erro no clima: {result.error}")
    
    # Teste calculadora
    result = registry.execute_tool("calculator", expression="10 + 5")
    result = asyncio.run(result)
    
    if result.success:
        print(f"✅ Cálculo: {result.data['result']}")
    else:
        print(f"❌ Erro no cálculo: {result.error}")

def demo_agent_basic():
    """Demonstração básica do agente com ferramentas"""
    print("\n🤖 Demonstração: Agente Básico com Ferramentas")
    print("=" * 40)
    
    # Verificar se temos chave Groq
    from agent.config import AgentConfig
    config = AgentConfig()
    
    if not config.groq_api_key:
        print("⚠️  Chave Groq não configurada. Pulando demonstração do agente.")
        print("💡 Configure GROQ_API_KEY no arquivo .env para testar o agente completo.")
        return
    
    from agent import LangGraphAgent
    from tools.weather import create_weather_tool
    from tools.calculator import create_calculator_tool
    
    # Criar ferramentas
    tools = [
        create_weather_tool(api_key=None),  # Mock data
        create_calculator_tool(precision=2)
    ]
    
    # Criar agente
    agent = LangGraphAgent(config, tools=tools)
    
    print(f"🤖 Agente criado: {config.agent_name}")
    print(f"🛠️  Ferramentas: {agent.list_enabled_tools()}")
    
    # Teste simples (sem chamada real ao LLM para evitar erros)
    print(f"✅ Agente configurado com sucesso!")
    print(f"📋 Informações das ferramentas:")
    for name, info in agent.get_tool_info().items():
        print(f"  - {name}: {info['description']}")

def main():
    """Função principal"""
    print("🛠️  Sistema de Ferramentas LangGraph - Demonstração")
    print("=" * 60)
    
    try:
        demo_weather_tool()
        demo_calculator_tool()
        demo_tool_registry()
        demo_agent_basic()
        
        print("\n🎉 Todas as demonstrações concluídas com sucesso!")
        print("📚 O sistema de ferramentas está funcionando corretamente.")
        
    except Exception as e:
        print(f"\n❌ Erro durante a demonstração: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
