"""
Basic usage example for the LangGraph Agent
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from agent import LangGraphAgent, AgentConfig


def main():
    """Main function demonstrating basic usage"""

    # Create configuration
    config = AgentConfig()

    # Check if Groq API key is set
    if not config.groq_api_key:
        print("Please set your GROQ_API_KEY in the .env file")
        return

    # Create the agent
    agent = LangGraphAgent(config)

    print(f"🤖 {config.agent_name} is ready!")
    print(f"📝 {config.agent_description}")
    print("-" * 50)

    # Example interactions
    examples = [
        "Hello! Can you introduce yourself?",
        "What can you help me with?",
        "Explain what LangGraph is in simple terms.",
        "How do you process tasks?"
    ]

    for i, example in enumerate(examples, 1):
        print(f"\n🔹 Example {i}: {example}")
        try:
            response = agent.run(example)
            print(f"🤖 Response: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")

        print("-" * 30)


async def async_main():
    """Async version of the main function"""

    # Create configuration
    config = AgentConfig()

    # Check if Groq API key is set
    if not config.groq_api_key:
        print("Please set your GROQ_API_KEY in the .env file")
        return

    # Create the agent
    agent = LangGraphAgent(config)

    print(f"🤖 {config.agent_name} is ready! (Async mode)")
    print(f"📝 {config.agent_description}")
    print("-" * 50)

    # Example async interaction
    user_input = "Can you explain how you work asynchronously?"
    print(f"\n🔹 Async Example: {user_input}")

    try:
        response = await agent.arun(user_input)
        print(f"🤖 Response: {response}")
    except Exception as e:
        print(f"❌ Error: {e}")


def interactive_mode():
    """Interactive mode for chatting with the agent"""

    # Create configuration
    config = AgentConfig()

    # Check if Groq API key is set
    if not config.groq_api_key:
        print("Please set your GROQ_API_KEY in the .env file")
        return

    # Create the agent
    agent = LangGraphAgent(config)

    print(f"🤖 {config.agent_name} - Interactive Mode")
    print(f"📝 {config.agent_description}")
    print("💡 Type 'quit' or 'exit' to end the conversation")
    print("-" * 50)

    while True:
        try:
            user_input = input("\n👤 You: ").strip()

            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break

            if not user_input:
                continue

            print("🤖 Thinking...")
            response = agent.run(user_input)
            print(f"🤖 {config.agent_name}: {response}")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="LangGraph Agent Examples")
    parser.add_argument(
        "--mode",
        choices=["basic", "async", "interactive"],
        default="basic",
        help="Mode to run the example in"
    )

    args = parser.parse_args()

    if args.mode == "basic":
        main()
    elif args.mode == "async":
        asyncio.run(async_main())
    elif args.mode == "interactive":
        interactive_mode()
