# LangGraph Agent Project

Um projeto Python para desenvolvimento de agentes usando LangGraph, uma biblioteca para construção de aplicações com múltiplos agentes usando grafos de estado.

## 🚀 Características

- **LangGraph**: Framework moderno para construção de agentes baseados em grafos
- **Groq Integration**: Integração com modelos Llama via Groq para inferência ultra-rápida
- **Configuração Flexível**: Sistema de configuração baseado em Pydantic e variáveis de ambiente
- **Suporte Assíncrono**: Execução síncrona e assíncrona
- **Testes Incluídos**: Suite de testes com pytest
- **Exemplos Práticos**: Exemplos de uso básico e interativo

## 📁 Estrutura do Projeto

```
langgraph-agent/
├── src/
│   └── agent/
│       ├── __init__.py
│       ├── agent.py          # Implementação principal do agente
│       └── config.py         # Configurações do agente
├── tests/
│   ├── __init__.py
│   ├── test_agent.py         # Testes do agente
│   └── test_config.py        # Testes de configuração
├── examples/
│   └── basic_usage.py        # Exemplos de uso
├── docs/                     # Documentação (futuro)
├── venv/                     # Ambiente virtual Python
├── requirements.txt          # Dependências do projeto
├── pyproject.toml           # Configuração do projeto
├── .env.example             # Exemplo de variáveis de ambiente
└── README.md                # Este arquivo
```

## 🛠️ Instalação

### 1. Clone o repositório (se aplicável)
```bash
git clone <repository-url>
cd langgraph-agent
```

### 2. Ative o ambiente virtual
```bash
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows
```

### 3. Instale as dependências
As dependências já foram instaladas durante a configuração inicial. Se precisar reinstalar:
```bash
pip install -r requirements.txt
```

### 4. Configure as variáveis de ambiente
```bash
cp .env.example .env
```

Edite o arquivo `.env` e adicione sua chave da Groq:
```env
GROQ_API_KEY=sua_chave_groq_aqui
```

> 📝 **Como obter sua chave Groq**: Consulte o arquivo [GROQ_SETUP.md](GROQ_SETUP.md) para instruções detalhadas sobre como criar uma conta e obter sua chave API gratuita.

## 🎯 Uso Básico

### Exemplo Simples

```python
from src.agent import LangGraphAgent, AgentConfig

# Criar configuração
config = AgentConfig()

# Criar o agente
agent = LangGraphAgent(config)

# Executar uma tarefa
response = agent.run("Olá! Como você funciona?")
print(response)
```

### Uso Assíncrono

```python
import asyncio
from src.agent import LangGraphAgent, AgentConfig

async def main():
    config = AgentConfig()
    agent = LangGraphAgent(config)

    response = await agent.arun("Explique o LangGraph")
    print(response)

asyncio.run(main())
```

## 📚 Exemplos

### Executar exemplos básicos
```bash
cd examples
python basic_usage.py --mode basic
```

### Modo interativo
```bash
python basic_usage.py --mode interactive
```

### Modo assíncrono
```bash
python basic_usage.py --mode async
```

### Demonstração completa do Groq
```bash
python examples/groq_llama_example.py
```

## 🧪 Testes

### Executar todos os testes
```bash
pytest tests/
```

### Executar testes com cobertura
```bash
pytest tests/ --cov=src/agent --cov-report=html
```

### Executar testes específicos
```bash
pytest tests/test_agent.py -v
pytest tests/test_config.py -v
```

## ⚙️ Configuração

O agente pode ser configurado através de variáveis de ambiente ou diretamente no código:

### Variáveis de Ambiente

```env
# Groq
GROQ_API_KEY=sua_chave_aqui

# LangSmith (opcional)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=sua_chave_langsmith
LANGCHAIN_PROJECT=meu-projeto

# Agente
AGENT_NAME=MeuAgente
AGENT_DESCRIPTION=Descrição do meu agente

# Logging
LOG_LEVEL=INFO
```

### Configuração Programática

```python
from src.agent import AgentConfig

config = AgentConfig(
    groq_api_key="sua_chave",
    agent_name="MeuAgente",
    model_name="meta-llama/llama-4-maverick-17b-128e-instruct",
    temperature=0.7,
    max_tokens=2000
)
```

## 🏗️ Arquitetura do Agente

O agente LangGraph é construído usando um grafo de estado com os seguintes nós:

1. **analyze_task**: Analisa a tarefa recebida
2. **process_task**: Processa a tarefa e adiciona contexto
3. **generate_response**: Gera resposta usando o LLM

### Fluxo de Execução

```
Entrada do Usuário → analyze_task → process_task → generate_response → Resposta
```

## 🔧 Desenvolvimento

### Formatação de código
```bash
black src/ tests/ examples/
```

### Verificação de tipos
```bash
mypy src/
```

### Linting
```bash
flake8 src/ tests/ examples/
```

## 📝 Próximos Passos

- [ ] Adicionar mais tipos de nós ao grafo
- [ ] Implementar ferramentas personalizadas
- [ ] Adicionar suporte a memória persistente
- [ ] Criar interface web com Streamlit
- [ ] Adicionar mais exemplos de uso
- [ ] Documentação detalhada

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🆘 Suporte

Se você encontrar problemas ou tiver dúvidas:

1. Verifique se todas as dependências estão instaladas
2. Confirme que a chave da Groq está configurada corretamente
3. Execute os testes para verificar se tudo está funcionando
4. Consulte os exemplos para referência

## 📚 Recursos Úteis

- [Documentação do LangGraph](https://langchain-ai.github.io/langgraph/)
- [Documentação do LangChain](https://python.langchain.com/)
- [Groq API Documentation](https://console.groq.com/docs)
- [Pydantic Documentation](https://docs.pydantic.dev/)
