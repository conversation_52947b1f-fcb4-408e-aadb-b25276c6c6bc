#!/usr/bin/env python3
"""
Teste simples do sistema de ferramentas
"""

import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_tools_import():
    """Test basic tool imports"""
    try:
        print("🧪 Testando importações...")
        
        # Test base imports
        from tools.base import BaseTool, ToolRegistry, ToolConfig, ToolResult
        print("✅ Base tools imported")
        
        # Test specific tools
        from tools.weather import WeatherTool, create_weather_tool
        print("✅ Weather tool imported")
        
        from tools.calculator import CalculatorTool, create_calculator_tool
        print("✅ Calculator tool imported")
        
        from tools.web_search import WebSearchTool, create_web_search_tool
        print("✅ Web search tool imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_tool_creation():
    """Test tool creation"""
    try:
        print("\n🔧 Testando criação de ferramentas...")
        
        from tools.weather import create_weather_tool
        from tools.calculator import create_calculator_tool
        
        # Create tools
        weather_tool = create_weather_tool(api_key=None)
        calc_tool = create_calculator_tool()
        
        print(f"✅ Weather tool: {weather_tool.name}")
        print(f"✅ Calculator tool: {calc_tool.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool creation error: {e}")
        return False

def test_tool_registry():
    """Test tool registry"""
    try:
        print("\n📋 Testando registry de ferramentas...")
        
        from tools.base import ToolRegistry
        from tools.weather import create_weather_tool
        
        # Create registry
        registry = ToolRegistry()
        
        # Add tool
        weather_tool = create_weather_tool(api_key=None)
        registry.add_tool(weather_tool)
        
        print(f"✅ Registry created with tools: {registry.list_tools()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Registry error: {e}")
        return False

def test_agent_with_tools():
    """Test agent with tools (without API calls)"""
    try:
        print("\n🤖 Testando agente com ferramentas...")
        
        from agent import LangGraphAgent, AgentConfig
        from tools.weather import create_weather_tool
        from tools.calculator import create_calculator_tool
        
        # Create tools
        tools = [
            create_weather_tool(api_key=None),
            create_calculator_tool()
        ]
        
        # Create agent
        config = AgentConfig(groq_api_key="test-key")
        agent = LangGraphAgent(config, tools=tools)
        
        print(f"✅ Agent created with tools: {agent.list_enabled_tools()}")
        print(f"✅ Tool info: {list(agent.get_tool_info().keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🛠️  Teste do Sistema de Ferramentas")
    print("=" * 40)
    
    tests = [
        ("Importações", test_tools_import),
        ("Criação de Ferramentas", test_tool_creation),
        ("Registry", test_tool_registry),
        ("Agente com Ferramentas", test_agent_with_tools),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 Resultados: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 Todos os testes passaram! Sistema de ferramentas funcionando.")
    else:
        print("⚠️  Alguns testes falharam. Verifique os erros acima.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
