#!/bin/bash

# Script para ativar o ambiente virtual e configurar o projeto
echo "🚀 Ativando ambiente virtual do LangGraph Agent..."

# Ativar ambiente virtual
source venv/bin/activate

# Verificar se a ativação foi bem-sucedida
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ Ambiente virtual ativado: $VIRTUAL_ENV"
else
    echo "❌ Falha ao ativar ambiente virtual"
    exit 1
fi

# Verificar se o arquivo .env existe
if [ ! -f ".env" ]; then
    echo "⚠️  Arquivo .env não encontrado. Copiando .env.example..."
    cp .env.example .env
    echo "📝 Por favor, edite o arquivo .env e adicione sua chave da OpenAI"
fi

# Mostrar informações do projeto
echo ""
echo "🤖 LangGraph Agent - Ambiente Pronto!"
echo "======================================"
echo "📁 Diretório: $(pwd)"
echo "🐍 Python: $(python --version)"
echo "📦 Pip: $(pip --version)"
echo ""
echo "🔧 Comandos úteis:"
echo "  python test_setup.py              # Testar configuração"
echo "  python examples/basic_usage.py    # Exemplo básico"
echo "  pytest tests/                     # Executar testes"
echo "  jupyter lab                       # Abrir Jupyter Lab"
echo ""
echo "📚 Para mais informações, consulte o README.md"
echo ""

# Manter o shell ativo
exec "$SHELL"
