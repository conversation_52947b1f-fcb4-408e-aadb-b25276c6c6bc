"""
Configuration module for the LangGraph Agent
"""

import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class AgentConfig(BaseModel):
    """Configuration class for the LangGraph Agent"""

    # Groq Configuration
    groq_api_key: str = Field(
        default_factory=lambda: os.getenv("GROQ_API_KEY", ""),
        description="Groq API key"
    )

    # LangSmith Configuration
    langchain_tracing_v2: bool = Field(
        default_factory=lambda: os.getenv("LANGCHAIN_TRACING_V2", "false").lower() == "true",
        description="Enable LangSmith tracing"
    )
    langchain_endpoint: str = Field(
        default_factory=lambda: os.getenv("LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com"),
        description="LangSmith endpoint"
    )
    langchain_api_key: Optional[str] = Field(
        default_factory=lambda: os.getenv("LANGCHAIN_API_KEY"),
        description="LangSmith API key"
    )
    langchain_project: str = Field(
        default_factory=lambda: os.getenv("LANGCHAIN_PROJECT", "langgraph-agent"),
        description="LangSmith project name"
    )

    # Agent Configuration
    agent_name: str = Field(
        default_factory=lambda: os.getenv("AGENT_NAME", "LangGraphAgent"),
        description="Name of the agent"
    )
    agent_description: str = Field(
        default_factory=lambda: os.getenv("AGENT_DESCRIPTION", "A LangGraph-based agent for task automation"),
        description="Description of the agent"
    )

    # Model Configuration
    model_name: str = Field(
        default="meta-llama/llama-4-maverick-17b-128e-instruct",
        description="Name of the language model to use"
    )
    temperature: float = Field(
        default=0.1,
        description="Temperature for the language model"
    )
    max_tokens: int = Field(
        default=1000,
        description="Maximum number of tokens for model responses"
    )

    # Logging Configuration
    log_level: str = Field(
        default_factory=lambda: os.getenv("LOG_LEVEL", "INFO"),
        description="Logging level"
    )

    def validate_config(self) -> bool:
        """Validate the configuration"""
        if not self.groq_api_key:
            raise ValueError("Groq API key is required")
        return True
