"""
Main LangGraph Agent implementation
"""

import logging
from typing import Dict, Any, List, Optional
from langchain_groq import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict, Annotated
from .config import AgentConfig

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgentState(TypedDict):
    """State definition for the LangGraph agent"""
    messages: Annotated[List[Any], add_messages]
    current_task: Optional[str]
    context: Dict[str, Any]


class LangGraphAgent:
    """
    A LangGraph-based agent for task automation
    """

    def __init__(self, config: Optional[AgentConfig] = None):
        """Initialize the LangGraph Agent"""
        self.config = config or AgentConfig()
        self.config.validate_config()

        # Initialize the language model
        self.llm = ChatGroq(
            api_key=self.config.groq_api_key,
            model=self.config.model_name,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens
        )

        # Build the graph
        self.graph = self._build_graph()

        logger.info(f"Initialized {self.config.agent_name}")

    def _build_graph(self) -> StateGraph:
        """Build the LangGraph state graph"""

        # Create the graph
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("analyze_task", self._analyze_task)
        workflow.add_node("process_task", self._process_task)
        workflow.add_node("generate_response", self._generate_response)

        # Add edges
        workflow.set_entry_point("analyze_task")
        workflow.add_edge("analyze_task", "process_task")
        workflow.add_edge("process_task", "generate_response")
        workflow.add_edge("generate_response", END)

        # Compile the graph
        return workflow.compile()

    def _analyze_task(self, state: AgentState) -> AgentState:
        """Analyze the incoming task"""
        logger.info("Analyzing task...")

        messages = state["messages"]
        if messages:
            last_message = messages[-1]
            if isinstance(last_message, HumanMessage):
                state["current_task"] = last_message.content
                logger.info(f"Task identified: {state['current_task']}")

        return state

    def _process_task(self, state: AgentState) -> AgentState:
        """Process the current task"""
        logger.info("Processing task...")

        current_task = state.get("current_task", "")

        # Add task processing logic here
        # For now, we'll just add some context
        state["context"] = {
            "task_type": "general",
            "complexity": "medium",
            "processed_at": "now"
        }

        logger.info("Task processed successfully")
        return state

    def _generate_response(self, state: AgentState) -> AgentState:
        """Generate response using the LLM"""
        logger.info("Generating response...")

        messages = state["messages"]
        current_task = state.get("current_task", "")
        context = state.get("context", {})

        # Create system message with context
        system_message = SystemMessage(
            content=f"""You are {self.config.agent_name}, {self.config.agent_description}.

            Current task: {current_task}
            Context: {context}

            Please provide a helpful and accurate response to the user's request."""
        )

        # Prepare messages for the LLM
        llm_messages = [system_message] + messages

        # Generate response
        response = self.llm.invoke(llm_messages)

        # Add the response to messages
        state["messages"].append(response)

        logger.info("Response generated successfully")
        return state

    def run(self, user_input: str) -> str:
        """Run the agent with user input"""
        logger.info(f"Running agent with input: {user_input}")

        # Create initial state
        initial_state = AgentState(
            messages=[HumanMessage(content=user_input)],
            current_task=None,
            context={}
        )

        # Run the graph
        result = self.graph.invoke(initial_state)

        # Extract the response
        if result["messages"]:
            last_message = result["messages"][-1]
            if isinstance(last_message, AIMessage):
                return last_message.content

        return "I apologize, but I couldn't generate a response."

    async def arun(self, user_input: str) -> str:
        """Async version of run"""
        logger.info(f"Running agent asynchronously with input: {user_input}")

        # Create initial state
        initial_state = AgentState(
            messages=[HumanMessage(content=user_input)],
            current_task=None,
            context={}
        )

        # Run the graph asynchronously
        result = await self.graph.ainvoke(initial_state)

        # Extract the response
        if result["messages"]:
            last_message = result["messages"][-1]
            if isinstance(last_message, AIMessage):
                return last_message.content

        return "I apologize, but I couldn't generate a response."
