"""
Main LangGraph Agent implementation
"""

import logging
import json
from typing import Dict, Any, List, Optional
from langchain_groq import ChatG<PERSON>q
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, ToolMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict, Annotated
from .config import AgentConfig
from ..tools import ToolRegistry, BaseTool

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgentState(TypedDict):
    """State definition for the LangGraph agent"""
    messages: Annotated[List[Any], add_messages]
    current_task: Optional[str]
    context: Dict[str, Any]
    tool_calls: List[Dict[str, Any]]
    needs_tools: bool


class LangGraphAgent:
    """
    A LangGraph-based agent for task automation
    """

    def __init__(self, config: Optional[AgentConfig] = None, tools: Optional[List[BaseTool]] = None):
        """Initialize the LangGraph Agent"""
        self.config = config or AgentConfig()
        self.config.validate_config()

        # Initialize tool registry
        self.tool_registry = ToolRegistry()
        if tools:
            for tool in tools:
                self.tool_registry.add_tool(tool)

        # Initialize the language model
        self.llm = ChatGroq(
            api_key=self.config.groq_api_key,
            model=self.config.model_name,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens
        )

        # Bind tools to LLM if available
        if self.tool_registry.get_enabled_tools():
            tool_schemas = self.tool_registry.get_tool_schemas()
            self.llm_with_tools = self.llm.bind_tools(tool_schemas)
        else:
            self.llm_with_tools = self.llm

        # Build the graph
        self.graph = self._build_graph()

        logger.info(f"Initialized {self.config.agent_name} with {len(self.tool_registry.get_enabled_tools())} tools")

    def _build_graph(self) -> StateGraph:
        """Build the LangGraph state graph"""

        # Create the graph
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("analyze_task", self._analyze_task)
        workflow.add_node("process_task", self._process_task)
        workflow.add_node("generate_response", self._generate_response)
        workflow.add_node("execute_tools", self._execute_tools)

        # Add edges
        workflow.set_entry_point("analyze_task")
        workflow.add_edge("analyze_task", "process_task")
        workflow.add_edge("process_task", "generate_response")

        # Conditional edge: check if tools are needed
        workflow.add_conditional_edges(
            "generate_response",
            self._should_use_tools,
            {
                "tools": "execute_tools",
                "end": END
            }
        )
        workflow.add_edge("execute_tools", "generate_response")

        # Compile the graph
        return workflow.compile()

    def _analyze_task(self, state: AgentState) -> AgentState:
        """Analyze the incoming task"""
        logger.info("Analyzing task...")

        messages = state["messages"]
        if messages:
            last_message = messages[-1]
            if isinstance(last_message, HumanMessage):
                state["current_task"] = last_message.content
                logger.info(f"Task identified: {state['current_task']}")

        return state

    def _process_task(self, state: AgentState) -> AgentState:
        """Process the current task"""
        logger.info("Processing task...")

        current_task = state.get("current_task", "")

        # Add task processing logic here
        # For now, we'll just add some context
        state["context"] = {
            "task_type": "general",
            "complexity": "medium",
            "processed_at": "now"
        }

        logger.info("Task processed successfully")
        return state

    def _generate_response(self, state: AgentState) -> AgentState:
        """Generate response using the LLM"""
        logger.info("Generating response...")

        messages = state["messages"]
        current_task = state.get("current_task", "")
        context = state.get("context", {})

        # Create system message with context and available tools
        available_tools = list(self.tool_registry.get_enabled_tools().keys())
        tools_info = f"Available tools: {', '.join(available_tools)}" if available_tools else "No tools available"

        system_message = SystemMessage(
            content=f"""You are {self.config.agent_name}, {self.config.agent_description}.

            Current task: {current_task}
            Context: {context}
            {tools_info}

            If you need to use tools to answer the user's question, call the appropriate tool functions.
            Otherwise, provide a helpful and accurate response directly."""
        )

        # Prepare messages for the LLM
        llm_messages = [system_message] + messages

        # Generate response (with tools if available)
        response = self.llm_with_tools.invoke(llm_messages)

        # Check if response contains tool calls
        if hasattr(response, 'tool_calls') and response.tool_calls:
            state["tool_calls"] = response.tool_calls
            state["needs_tools"] = True
        else:
            state["tool_calls"] = []
            state["needs_tools"] = False

        # Add the response to messages
        state["messages"].append(response)

        logger.info("Response generated successfully")
        return state

    def _should_use_tools(self, state: AgentState) -> str:
        """Decide whether to use tools or end the conversation"""
        if state.get("needs_tools", False) and state.get("tool_calls"):
            return "tools"
        return "end"

    def _execute_tools(self, state: AgentState) -> AgentState:
        """Execute tool calls"""
        logger.info("Executing tools...")

        tool_calls = state.get("tool_calls", [])

        for tool_call in tool_calls:
            try:
                tool_name = tool_call["name"]
                tool_args = tool_call.get("args", {})
                tool_id = tool_call.get("id", "unknown")

                logger.info(f"Executing tool: {tool_name} with args: {tool_args}")

                # Execute tool
                result = self.tool_registry.execute_tool(tool_name, **tool_args)

                # Handle async result
                if hasattr(result, '__await__'):
                    import asyncio
                    try:
                        loop = asyncio.get_event_loop()
                        result = loop.run_until_complete(result)
                    except RuntimeError:
                        result = asyncio.run(result)

                # Create tool message
                if result.success:
                    tool_message = ToolMessage(
                        content=json.dumps(result.data, indent=2),
                        tool_call_id=tool_id
                    )
                    logger.info(f"Tool {tool_name} executed successfully")
                else:
                    tool_message = ToolMessage(
                        content=f"Error: {result.error}",
                        tool_call_id=tool_id
                    )
                    logger.error(f"Tool {tool_name} failed: {result.error}")

                # Add tool result to messages
                state["messages"].append(tool_message)

            except Exception as e:
                logger.error(f"Error executing tool call: {e}")
                error_message = ToolMessage(
                    content=f"Error executing tool: {str(e)}",
                    tool_call_id=tool_call.get("id", "unknown")
                )
                state["messages"].append(error_message)

        # Reset tool state
        state["tool_calls"] = []
        state["needs_tools"] = False

        logger.info("Tools execution completed")
        return state

    def run(self, user_input: str) -> str:
        """Run the agent with user input"""
        logger.info(f"Running agent with input: {user_input}")

        # Create initial state
        initial_state = AgentState(
            messages=[HumanMessage(content=user_input)],
            current_task=None,
            context={},
            tool_calls=[],
            needs_tools=False
        )

        # Run the graph
        result = self.graph.invoke(initial_state)

        # Extract the response
        if result["messages"]:
            last_message = result["messages"][-1]
            if isinstance(last_message, AIMessage):
                return last_message.content

        return "I apologize, but I couldn't generate a response."

    async def arun(self, user_input: str) -> str:
        """Async version of run"""
        logger.info(f"Running agent asynchronously with input: {user_input}")

        # Create initial state
        initial_state = AgentState(
            messages=[HumanMessage(content=user_input)],
            current_task=None,
            context={},
            tool_calls=[],
            needs_tools=False
        )

        # Run the graph asynchronously
        result = await self.graph.ainvoke(initial_state)

        # Extract the response
        if result["messages"]:
            last_message = result["messages"][-1]
            if isinstance(last_message, AIMessage):
                return last_message.content

        return "I apologize, but I couldn't generate a response."

    def add_tool(self, tool: BaseTool) -> None:
        """Add a tool to the agent"""
        self.tool_registry.add_tool(tool)

        # Rebuild LLM with tools
        if self.tool_registry.get_enabled_tools():
            tool_schemas = self.tool_registry.get_tool_schemas()
            self.llm_with_tools = self.llm.bind_tools(tool_schemas)

        logger.info(f"Added tool: {tool.name}")

    def remove_tool(self, tool_name: str) -> None:
        """Remove a tool from the agent"""
        self.tool_registry.remove_tool(tool_name)

        # Rebuild LLM with tools
        if self.tool_registry.get_enabled_tools():
            tool_schemas = self.tool_registry.get_tool_schemas()
            self.llm_with_tools = self.llm.bind_tools(tool_schemas)
        else:
            self.llm_with_tools = self.llm

        logger.info(f"Removed tool: {tool_name}")

    def list_tools(self) -> List[str]:
        """List all available tools"""
        return self.tool_registry.list_tools()

    def list_enabled_tools(self) -> List[str]:
        """List enabled tools"""
        return self.tool_registry.list_enabled_tools()

    def get_tool_info(self) -> Dict[str, Any]:
        """Get information about all tools"""
        tools_info = {}
        for name, tool in self.tool_registry.get_all_tools().items():
            tools_info[name] = {
                "name": tool.name,
                "description": tool.description,
                "enabled": tool.enabled,
                "required_parameters": tool.required_parameters,
                "optional_parameters": tool.optional_parameters
            }
        return tools_info