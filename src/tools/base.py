"""
Base classes for tools system
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Type
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class ToolConfig(BaseModel):
    """Base configuration for tools"""
    enabled: bool = Field(default=True, description="Whether the tool is enabled")
    name: str = Field(description="Tool name")
    description: str = Field(description="Tool description")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Tool-specific parameters")


class ToolResult(BaseModel):
    """Result from tool execution"""
    success: bool = Field(description="Whether the tool execution was successful")
    data: Any = Field(default=None, description="Tool execution result data")
    error: Optional[str] = Field(default=None, description="Error message if execution failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class BaseTool(ABC):
    """Base class for all tools"""
    
    def __init__(self, config: ToolConfig):
        """Initialize the tool with configuration"""
        self.config = config
        self.name = config.name
        self.description = config.description
        self.enabled = config.enabled
        
        # Validate configuration
        self._validate_config()
        
        logger.info(f"Initialized tool: {self.name}")
    
    @abstractmethod
    def _validate_config(self) -> None:
        """Validate tool-specific configuration"""
        pass
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the tool with given parameters"""
        pass
    
    def execute_sync(self, **kwargs) -> ToolResult:
        """Synchronous wrapper for execute method"""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.execute(**kwargs))
        except RuntimeError:
            # No event loop running, create a new one
            return asyncio.run(self.execute(**kwargs))
    
    @property
    @abstractmethod
    def required_parameters(self) -> List[str]:
        """List of required parameters for this tool"""
        pass
    
    @property
    @abstractmethod
    def optional_parameters(self) -> List[str]:
        """List of optional parameters for this tool"""
        pass
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for LLM function calling"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": self._get_parameter_schema(),
                "required": self.required_parameters
            }
        }
    
    @abstractmethod
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for function calling"""
        pass
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(name='{self.name}', enabled={self.enabled})"
    
    def __repr__(self) -> str:
        return self.__str__()


class ToolRegistry:
    """Registry for managing available tools"""
    
    def __init__(self):
        """Initialize the tool registry"""
        self._tools: Dict[str, BaseTool] = {}
        self._tool_classes: Dict[str, Type[BaseTool]] = {}
        
        logger.info("Initialized ToolRegistry")
    
    def register_tool_class(self, tool_class: Type[BaseTool], name: Optional[str] = None) -> None:
        """Register a tool class"""
        tool_name = name or tool_class.__name__
        self._tool_classes[tool_name] = tool_class
        logger.info(f"Registered tool class: {tool_name}")
    
    def create_tool(self, tool_name: str, config: ToolConfig) -> BaseTool:
        """Create a tool instance from registered class"""
        if tool_name not in self._tool_classes:
            raise ValueError(f"Tool class '{tool_name}' not registered")
        
        tool_class = self._tool_classes[tool_name]
        tool_instance = tool_class(config)
        
        return tool_instance
    
    def add_tool(self, tool: BaseTool) -> None:
        """Add a tool instance to the registry"""
        if not tool.enabled:
            logger.warning(f"Adding disabled tool: {tool.name}")
        
        self._tools[tool.name] = tool
        logger.info(f"Added tool to registry: {tool.name}")
    
    def remove_tool(self, tool_name: str) -> None:
        """Remove a tool from the registry"""
        if tool_name in self._tools:
            del self._tools[tool_name]
            logger.info(f"Removed tool from registry: {tool_name}")
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get a tool by name"""
        return self._tools.get(tool_name)
    
    def get_enabled_tools(self) -> Dict[str, BaseTool]:
        """Get all enabled tools"""
        return {name: tool for name, tool in self._tools.items() if tool.enabled}
    
    def get_all_tools(self) -> Dict[str, BaseTool]:
        """Get all tools (enabled and disabled)"""
        return self._tools.copy()
    
    def get_tool_schemas(self) -> List[Dict[str, Any]]:
        """Get schemas for all enabled tools for LLM function calling"""
        return [tool.get_schema() for tool in self.get_enabled_tools().values()]
    
    def list_tools(self) -> List[str]:
        """List all tool names"""
        return list(self._tools.keys())
    
    def list_enabled_tools(self) -> List[str]:
        """List enabled tool names"""
        return [name for name, tool in self._tools.items() if tool.enabled]
    
    async def execute_tool(self, tool_name: str, **kwargs) -> ToolResult:
        """Execute a tool by name"""
        tool = self.get_tool(tool_name)
        if not tool:
            return ToolResult(
                success=False,
                error=f"Tool '{tool_name}' not found"
            )
        
        if not tool.enabled:
            return ToolResult(
                success=False,
                error=f"Tool '{tool_name}' is disabled"
            )
        
        try:
            return await tool.execute(**kwargs)
        except Exception as e:
            logger.error(f"Error executing tool '{tool_name}': {e}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    def __len__(self) -> int:
        return len(self._tools)
    
    def __contains__(self, tool_name: str) -> bool:
        return tool_name in self._tools
    
    def __str__(self) -> str:
        enabled_count = len(self.get_enabled_tools())
        total_count = len(self._tools)
        return f"ToolRegistry({enabled_count}/{total_count} tools enabled)"
