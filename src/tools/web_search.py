"""
Web search tool for searching information on the internet
"""

import aiohttp
import asyncio
from typing import List, Dict, Any
from .base import BaseTool, ToolConfig, ToolResult
import logging

logger = logging.getLogger(__name__)


class WebSearchTool(BaseTool):
    """Tool for searching information on the web"""
    
    def __init__(self, config: ToolConfig):
        """Initialize web search tool"""
        super().__init__(config)
        self.max_results = config.parameters.get("max_results", 5)
        self.search_engine = config.parameters.get("search_engine", "duckduckgo")
        self.safe_search = config.parameters.get("safe_search", True)
    
    def _validate_config(self) -> None:
        """Validate web search tool configuration"""
        if self.max_results < 1 or self.max_results > 20:
            raise ValueError("max_results must be between 1 and 20")
        
        if self.search_engine not in ["duckduckgo", "mock"]:
            logger.warning(f"Unsupported search engine: {self.search_engine}. Using mock search.")
            self.search_engine = "mock"
    
    @property
    def required_parameters(self) -> List[str]:
        """Required parameters for web search tool"""
        return ["query"]
    
    @property
    def optional_parameters(self) -> List[str]:
        """Optional parameters for web search tool"""
        return ["max_results", "safe_search"]
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for function calling"""
        return {
            "query": {
                "type": "string",
                "description": "Search query to look for on the web"
            },
            "max_results": {
                "type": "integer",
                "description": "Maximum number of search results to return",
                "minimum": 1,
                "maximum": 20
            },
            "safe_search": {
                "type": "boolean",
                "description": "Enable safe search filtering"
            }
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute web search"""
        query = kwargs.get("query")
        max_results = kwargs.get("max_results", self.max_results)
        safe_search = kwargs.get("safe_search", self.safe_search)
        
        if not query:
            return ToolResult(
                success=False,
                error="Query parameter is required"
            )
        
        try:
            if self.search_engine == "duckduckgo":
                return await self._search_duckduckgo(query, max_results, safe_search)
            else:
                return await self._mock_search(query, max_results, safe_search)
                
        except Exception as e:
            logger.error(f"Error searching for '{query}': {e}")
            return ToolResult(
                success=False,
                error=f"Failed to perform web search: {str(e)}"
            )
    
    async def _mock_search(self, query: str, max_results: int, safe_search: bool) -> ToolResult:
        """Mock search for testing purposes"""
        # Simulate search delay
        await asyncio.sleep(1.0)
        
        # Generate mock results
        mock_results = []
        for i in range(min(max_results, 3)):
            mock_results.append({
                "title": f"Mock Result {i+1} for '{query}'",
                "url": f"https://example.com/result-{i+1}",
                "snippet": f"This is a mock search result snippet for query '{query}'. "
                          f"It contains relevant information about the search topic.",
                "source": "example.com"
            })
        
        return ToolResult(
            success=True,
            data={
                "query": query,
                "results": mock_results,
                "total_results": len(mock_results),
                "search_engine": "mock",
                "safe_search": safe_search
            },
            metadata={
                "is_mock": True,
                "search_time": "1.0s"
            }
        )
    
    async def _search_duckduckgo(self, query: str, max_results: int, safe_search: bool) -> ToolResult:
        """Search using DuckDuckGo Instant Answer API"""
        
        # DuckDuckGo Instant Answer API
        url = "https://api.duckduckgo.com/"
        params = {
            "q": query,
            "format": "json",
            "no_html": "1",
            "skip_disambig": "1",
            "safe_search": "strict" if safe_search else "moderate"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        results = []
                        
                        # Parse instant answer
                        if data.get("AbstractText"):
                            results.append({
                                "title": data.get("Heading", query),
                                "url": data.get("AbstractURL", ""),
                                "snippet": data.get("AbstractText", ""),
                                "source": data.get("AbstractSource", "DuckDuckGo")
                            })
                        
                        # Parse related topics
                        for topic in data.get("RelatedTopics", [])[:max_results-len(results)]:
                            if isinstance(topic, dict) and topic.get("Text"):
                                results.append({
                                    "title": topic.get("Text", "").split(" - ")[0],
                                    "url": topic.get("FirstURL", ""),
                                    "snippet": topic.get("Text", ""),
                                    "source": "DuckDuckGo"
                                })
                        
                        # If no results, create a basic response
                        if not results:
                            results.append({
                                "title": f"Search results for: {query}",
                                "url": f"https://duckduckgo.com/?q={query.replace(' ', '+')}",
                                "snippet": f"No instant answers found for '{query}'. Try searching on DuckDuckGo for more results.",
                                "source": "DuckDuckGo"
                            })
                        
                        return ToolResult(
                            success=True,
                            data={
                                "query": query,
                                "results": results[:max_results],
                                "total_results": len(results),
                                "search_engine": "duckduckgo",
                                "safe_search": safe_search
                            },
                            metadata={
                                "is_mock": False,
                                "api_response_code": response.status
                            }
                        )
                    else:
                        # Fallback to mock search
                        logger.warning(f"DuckDuckGo API returned {response.status}, using mock search")
                        return await self._mock_search(query, max_results, safe_search)
                        
        except asyncio.TimeoutError:
            logger.warning("DuckDuckGo API timeout, using mock search")
            return await self._mock_search(query, max_results, safe_search)
        except Exception as e:
            logger.warning(f"DuckDuckGo API error: {e}, using mock search")
            return await self._mock_search(query, max_results, safe_search)


# Factory function for easy tool creation
def create_web_search_tool(max_results: int = 5, search_engine: str = "duckduckgo", 
                          safe_search: bool = True, enabled: bool = True) -> WebSearchTool:
    """Create a web search tool with given configuration"""
    config = ToolConfig(
        name="web_search",
        description="Search for information on the web using DuckDuckGo",
        enabled=enabled,
        parameters={
            "max_results": max_results,
            "search_engine": search_engine,
            "safe_search": safe_search
        }
    )
    return WebSearchTool(config)
