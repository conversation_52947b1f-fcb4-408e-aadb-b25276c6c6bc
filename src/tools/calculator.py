"""
Calculator tool for mathematical operations
"""

import math
import re
from typing import List, Dict, Any
from .base import BaseTool, ToolConfig, ToolResult
import logging

logger = logging.getLogger(__name__)


class CalculatorTool(BaseTool):
    """Tool for performing mathematical calculations"""
    
    def __init__(self, config: ToolConfig):
        """Initialize calculator tool"""
        super().__init__(config)
        self.precision = config.parameters.get("precision", 10)
        self.allow_functions = config.parameters.get("allow_functions", True)
    
    def _validate_config(self) -> None:
        """Validate calculator tool configuration"""
        if self.precision < 1 or self.precision > 20:
            raise ValueError("Precision must be between 1 and 20")
    
    @property
    def required_parameters(self) -> List[str]:
        """Required parameters for calculator tool"""
        return ["expression"]
    
    @property
    def optional_parameters(self) -> List[str]:
        """Optional parameters for calculator tool"""
        return ["precision"]
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for function calling"""
        return {
            "expression": {
                "type": "string",
                "description": "Mathematical expression to evaluate (e.g., '2 + 3 * 4', 'sqrt(16)', 'sin(pi/2)')"
            },
            "precision": {
                "type": "integer",
                "description": "Number of decimal places for the result",
                "minimum": 1,
                "maximum": 20
            }
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute mathematical calculation"""
        expression = kwargs.get("expression")
        precision = kwargs.get("precision", self.precision)
        
        if not expression:
            return ToolResult(
                success=False,
                error="Expression parameter is required"
            )
        
        try:
            # Clean and validate expression
            cleaned_expr = self._clean_expression(expression)
            
            # Check for security issues
            if not self._is_safe_expression(cleaned_expr):
                return ToolResult(
                    success=False,
                    error="Expression contains unsafe operations"
                )
            
            # Evaluate expression
            result = self._evaluate_expression(cleaned_expr)
            
            # Format result
            if isinstance(result, float):
                if result.is_integer():
                    formatted_result = int(result)
                else:
                    formatted_result = round(result, precision)
            else:
                formatted_result = result
            
            return ToolResult(
                success=True,
                data={
                    "expression": expression,
                    "result": formatted_result,
                    "precision": precision,
                    "type": type(formatted_result).__name__
                },
                metadata={
                    "cleaned_expression": cleaned_expr,
                    "raw_result": result
                }
            )
            
        except ZeroDivisionError:
            return ToolResult(
                success=False,
                error="Division by zero"
            )
        except ValueError as e:
            return ToolResult(
                success=False,
                error=f"Invalid mathematical operation: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Error evaluating expression '{expression}': {e}")
            return ToolResult(
                success=False,
                error=f"Failed to evaluate expression: {str(e)}"
            )
    
    def _clean_expression(self, expression: str) -> str:
        """Clean and normalize the mathematical expression"""
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', expression.strip())
        
        # Replace common text representations
        replacements = {
            'x': '*',
            '×': '*',
            '÷': '/',
            '^': '**',
            'π': 'pi',
            'e': 'e'
        }
        
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)
        
        return cleaned
    
    def _is_safe_expression(self, expression: str) -> bool:
        """Check if expression is safe to evaluate"""
        # List of dangerous patterns
        dangerous_patterns = [
            r'__',  # Dunder methods
            r'import',  # Import statements
            r'exec',  # Code execution
            r'eval',  # Code evaluation
            r'open',  # File operations
            r'file',  # File operations
            r'input',  # User input
            r'raw_input',  # User input
            r'compile',  # Code compilation
            r'globals',  # Global variables
            r'locals',  # Local variables
            r'vars',  # Variables
            r'dir',  # Directory listing
            r'help',  # Help function
            r'exit',  # Exit function
            r'quit',  # Quit function
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, expression, re.IGNORECASE):
                return False
        
        return True
    
    def _evaluate_expression(self, expression: str) -> float:
        """Safely evaluate mathematical expression"""
        # Define safe mathematical functions
        safe_dict = {
            # Basic operations are handled by Python
            # Mathematical functions
            'abs': abs,
            'round': round,
            'min': min,
            'max': max,
            'sum': sum,
            # Math module functions
            'sqrt': math.sqrt,
            'pow': math.pow,
            'exp': math.exp,
            'log': math.log,
            'log10': math.log10,
            'log2': math.log2,
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
            'asin': math.asin,
            'acos': math.acos,
            'atan': math.atan,
            'atan2': math.atan2,
            'sinh': math.sinh,
            'cosh': math.cosh,
            'tanh': math.tanh,
            'degrees': math.degrees,
            'radians': math.radians,
            'ceil': math.ceil,
            'floor': math.floor,
            'factorial': math.factorial,
            # Constants
            'pi': math.pi,
            'e': math.e,
            'tau': math.tau,
            'inf': math.inf,
            'nan': math.nan,
        }
        
        # Only allow safe functions if enabled
        if not self.allow_functions:
            # Remove functions, keep only constants and basic operations
            safe_dict = {k: v for k, v in safe_dict.items() 
                        if k in ['pi', 'e', 'tau', 'inf', 'nan']}
        
        # Evaluate expression
        result = eval(expression, {"__builtins__": {}}, safe_dict)
        
        return result


# Factory function for easy tool creation
def create_calculator_tool(precision: int = 10, allow_functions: bool = True, enabled: bool = True) -> CalculatorTool:
    """Create a calculator tool with given configuration"""
    config = ToolConfig(
        name="calculator",
        description="Perform mathematical calculations and evaluate expressions",
        enabled=enabled,
        parameters={
            "precision": precision,
            "allow_functions": allow_functions
        }
    )
    return CalculatorTool(config)
