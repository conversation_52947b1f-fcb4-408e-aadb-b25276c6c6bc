"""
Weather tool for getting weather information
"""

import aiohttp
import asyncio
from typing import List, Dict, Any
from .base import BaseTool, ToolConfig, ToolResult
import logging

logger = logging.getLogger(__name__)


class WeatherTool(BaseTool):
    """Tool for getting weather information for a city"""
    
    def __init__(self, config: ToolConfig):
        """Initialize weather tool"""
        super().__init__(config)
        self.api_key = config.parameters.get("api_key")
        self.base_url = config.parameters.get("base_url", "http://api.openweathermap.org/data/2.5")
        self.units = config.parameters.get("units", "metric")  # metric, imperial, kelvin
    
    def _validate_config(self) -> None:
        """Validate weather tool configuration"""
        if not self.api_key:
            logger.warning("No API key provided for WeatherTool. Using mock data.")
    
    @property
    def required_parameters(self) -> List[str]:
        """Required parameters for weather tool"""
        return ["city"]
    
    @property
    def optional_parameters(self) -> List[str]:
        """Optional parameters for weather tool"""
        return ["country_code", "units"]
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for function calling"""
        return {
            "city": {
                "type": "string",
                "description": "Name of the city to get weather for"
            },
            "country_code": {
                "type": "string",
                "description": "Optional country code (e.g., 'US', 'BR', 'UK')"
            },
            "units": {
                "type": "string",
                "description": "Temperature units: 'metric' (Celsius), 'imperial' (Fahrenheit), or 'kelvin'",
                "enum": ["metric", "imperial", "kelvin"]
            }
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute weather lookup"""
        city = kwargs.get("city")
        country_code = kwargs.get("country_code")
        units = kwargs.get("units", self.units)
        
        if not city:
            return ToolResult(
                success=False,
                error="City parameter is required"
            )
        
        try:
            # If no API key, return mock data
            if not self.api_key:
                return await self._get_mock_weather(city, country_code, units)
            
            # Make real API call
            return await self._get_real_weather(city, country_code, units)
            
        except Exception as e:
            logger.error(f"Error getting weather for {city}: {e}")
            return ToolResult(
                success=False,
                error=f"Failed to get weather data: {str(e)}"
            )
    
    async def _get_mock_weather(self, city: str, country_code: str = None, units: str = "metric") -> ToolResult:
        """Get mock weather data for testing"""
        # Simulate API delay
        await asyncio.sleep(0.5)
        
        # Mock weather data
        temp_unit = "°C" if units == "metric" else "°F" if units == "imperial" else "K"
        base_temp = 22 if units == "metric" else 72 if units == "imperial" else 295
        
        location = f"{city}, {country_code}" if country_code else city
        
        mock_data = {
            "location": location,
            "temperature": base_temp,
            "feels_like": base_temp + 2,
            "humidity": 65,
            "description": "Partly cloudy",
            "wind_speed": 5.2,
            "units": units,
            "temp_unit": temp_unit,
            "is_mock": True
        }
        
        return ToolResult(
            success=True,
            data=mock_data,
            metadata={
                "source": "mock_data",
                "timestamp": "now"
            }
        )
    
    async def _get_real_weather(self, city: str, country_code: str = None, units: str = "metric") -> ToolResult:
        """Get real weather data from OpenWeatherMap API"""
        
        # Build query
        query = city
        if country_code:
            query = f"{city},{country_code}"
        
        url = f"{self.base_url}/weather"
        params = {
            "q": query,
            "appid": self.api_key,
            "units": units
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Parse response
                    weather_data = {
                        "location": f"{data['name']}, {data['sys']['country']}",
                        "temperature": data['main']['temp'],
                        "feels_like": data['main']['feels_like'],
                        "humidity": data['main']['humidity'],
                        "description": data['weather'][0]['description'].title(),
                        "wind_speed": data.get('wind', {}).get('speed', 0),
                        "units": units,
                        "temp_unit": "°C" if units == "metric" else "°F" if units == "imperial" else "K",
                        "is_mock": False
                    }
                    
                    return ToolResult(
                        success=True,
                        data=weather_data,
                        metadata={
                            "source": "openweathermap",
                            "timestamp": data.get('dt'),
                            "api_response_code": response.status
                        }
                    )
                else:
                    error_data = await response.json()
                    return ToolResult(
                        success=False,
                        error=f"API Error {response.status}: {error_data.get('message', 'Unknown error')}"
                    )


# Factory function for easy tool creation
def create_weather_tool(api_key: str = None, units: str = "metric", enabled: bool = True) -> WeatherTool:
    """Create a weather tool with given configuration"""
    config = ToolConfig(
        name="weather",
        description="Get current weather information for a specified city",
        enabled=enabled,
        parameters={
            "api_key": api_key,
            "units": units
        }
    )
    return WeatherTool(config)
